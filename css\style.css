/* Arabic RTL Neumorphic POS System Styles */

/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Root variables for neumorphic design */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* Neumorphic colors */
    --bg-color: #e0e5ec;
    --surface-color: #e0e5ec;
    --shadow-light: #ffffff;
    --shadow-dark: #a3b1c6;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --gradient-danger: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    --gradient-warning: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    
    /* Shadows */
    --shadow-neumorphic: 9px 9px 16px var(--shadow-dark), -9px -9px 16px var(--shadow-light);
    --shadow-neumorphic-inset: inset 9px 9px 16px var(--shadow-dark), inset -9px -9px 16px var(--shadow-light);
    --shadow-neumorphic-small: 5px 5px 10px var(--shadow-dark), -5px -5px 10px var(--shadow-light);
    --shadow-neumorphic-hover: 12px 12px 20px var(--shadow-dark), -12px -12px 20px var(--shadow-light);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--bg-color);
    color: var(--dark-color);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* Neumorphic card styles */
.neumorphic-card {
    background: var(--surface-color);
    border-radius: 20px;
    box-shadow: var(--shadow-neumorphic);
    padding: 2rem;
    border: none;
    transition: all 0.3s ease;
}

.neumorphic-card:hover {
    box-shadow: var(--shadow-neumorphic-hover);
    transform: translateY(-2px);
}

.neumorphic-card-small {
    background: var(--surface-color);
    border-radius: 15px;
    box-shadow: var(--shadow-neumorphic-small);
    padding: 1.5rem;
    border: none;
    transition: all 0.3s ease;
}

/* Neumorphic buttons */
.neumorphic-btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow-neumorphic-small);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.neumorphic-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-neumorphic);
    color: white;
}

.neumorphic-btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-neumorphic-inset);
}

.neumorphic-btn-secondary {
    background: var(--surface-color);
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    color: var(--dark-color);
    font-weight: 500;
    box-shadow: var(--shadow-neumorphic-small);
    transition: all 0.3s ease;
}

.neumorphic-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-neumorphic);
    color: var(--dark-color);
}

.neumorphic-btn-success {
    background: var(--gradient-success);
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow-neumorphic-small);
    transition: all 0.3s ease;
}

.neumorphic-btn-danger {
    background: var(--gradient-danger);
    border: none;
    border-radius: 15px;
    padding: 12px 24px;
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow-neumorphic-small);
    transition: all 0.3s ease;
}

/* Neumorphic inputs */
.neumorphic-input {
    background: var(--surface-color);
    border: none;
    border-radius: 15px;
    padding: 12px 16px;
    box-shadow: var(--shadow-neumorphic-inset);
    transition: all 0.3s ease;
    color: var(--dark-color);
}

.neumorphic-input:focus {
    outline: none;
    box-shadow: var(--shadow-neumorphic-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--surface-color);
    color: var(--dark-color);
}

.neumorphic-input::placeholder {
    color: #9ca3af;
}

/* Login page styles */
.login-body {
    background: var(--bg-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    max-width: 400px;
    width: 100%;
}

.logo-container {
    background: var(--surface-color);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: var(--shadow-neumorphic);
}

/* Navigation styles */
.navbar-neumorphic {
    background: var(--surface-color);
    box-shadow: var(--shadow-neumorphic-small);
    border-radius: 0 0 20px 20px;
    padding: 1rem 0;
}

.nav-link-neumorphic {
    background: var(--surface-color);
    border-radius: 12px;
    padding: 8px 16px;
    margin: 0 4px;
    color: var(--dark-color);
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-neumorphic-small);
}

.nav-link-neumorphic:hover,
.nav-link-neumorphic.active {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
}

/* Sidebar styles */
.sidebar-neumorphic {
    background: var(--surface-color);
    box-shadow: var(--shadow-neumorphic);
    border-radius: 20px;
    padding: 1.5rem;
    height: calc(100vh - 2rem);
    margin: 1rem;
}

.sidebar-item {
    background: var(--surface-color);
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    color: var(--dark-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-neumorphic-small);
}

.sidebar-item:hover,
.sidebar-item.active {
    background: var(--gradient-primary);
    color: white;
    transform: translateX(-4px);
}

.sidebar-item i {
    margin-left: 12px;
    width: 20px;
    text-align: center;
}

/* Table styles */
.table-neumorphic {
    background: var(--surface-color);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-neumorphic);
}

.table-neumorphic th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.table-neumorphic td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid rgba(163, 177, 198, 0.1);
}

.table-neumorphic tr:last-child td {
    border-bottom: none;
}

/* Modal styles */
.modal-neumorphic .modal-content {
    background: var(--surface-color);
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow-neumorphic);
}

.modal-neumorphic .modal-header {
    border-bottom: 1px solid rgba(163, 177, 198, 0.1);
    border-radius: 20px 20px 0 0;
}

.modal-neumorphic .modal-footer {
    border-top: 1px solid rgba(163, 177, 198, 0.1);
    border-radius: 0 0 20px 20px;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(224, 229, 236, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
}

/* Utility classes */
.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-danger {
    background: var(--gradient-danger);
}

.bg-gradient-warning {
    background: var(--gradient-warning);
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}
