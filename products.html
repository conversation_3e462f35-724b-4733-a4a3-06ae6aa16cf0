<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-neumorphic">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <a class="navbar-brand text-gradient-primary fw-bold" href="dashboard.html">
                <i class="bi bi-box-seam me-2"></i>
                إدارة المنتجات
            </a>
            
            <div class="d-flex align-items-center">
                <button class="btn neumorphic-btn-primary me-2" onclick="showAddProductModal()">
                    <i class="bi bi-plus-circle me-1"></i>
                    إضافة منتج
                </button>
                <div class="dropdown">
                    <button class="btn neumorphic-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="currentUserName">المدير</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar-neumorphic" id="sidebar">
        <div class="text-center mb-4">
            <div class="logo-container mx-auto mb-3">
                <i class="bi bi-box-seam text-primary"></i>
            </div>
            <h5 class="text-gradient-primary">إدارة المنتجات</h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="sidebar-item" href="dashboard.html">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            <a class="sidebar-item" href="pos.html">
                <i class="bi bi-cash-register"></i>
                نقطة البيع
            </a>
            <a class="sidebar-item active" href="products.html">
                <i class="bi bi-box-seam"></i>
                إدارة المنتجات
            </a>
            <a class="sidebar-item" href="customers.html">
                <i class="bi bi-people"></i>
                إدارة العملاء
            </a>
            <a class="sidebar-item" href="reports.html">
                <i class="bi bi-graph-up"></i>
                التقارير
            </a>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid p-4">
            <!-- Search and Filter Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control neumorphic-input" id="searchInput" 
                                           placeholder="ابحث بالاسم أو الباركود...">
                                    <button class="btn neumorphic-btn-primary" onclick="searchProducts()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select neumorphic-input" id="categoryFilter" onchange="filterProducts()">
                                    <option value="">جميع الفئات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select neumorphic-input" id="statusFilter" onchange="filterProducts()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="low_stock">مخزون قليل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn neumorphic-btn-secondary" onclick="resetFilters()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-primary">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="stat-value" id="totalProducts">0</div>
                        <div class="stat-label">إجمالي المنتجات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-success">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-value" id="activeProducts">0</div>
                        <div class="stat-label">منتجات نشطة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="stat-value" id="lowStockProducts">0</div>
                        <div class="stat-label">مخزون قليل</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-info">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stat-value" id="totalValue">0</div>
                        <div class="stat-label">قيمة المخزون</div>
                    </div>
                </div>
            </div>

            <!-- Products Table -->
            <div class="row">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>قائمة المنتجات</h4>
                            <div class="btn-group">
                                <button class="btn neumorphic-btn-primary" onclick="showAddProductModal()">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    إضافة منتج
                                </button>
                                <button class="btn neumorphic-btn-secondary" onclick="exportProducts()">
                                    <i class="bi bi-download me-1"></i>
                                    تصدير
                                </button>
                                <button class="btn neumorphic-btn-secondary" onclick="showImportModal()">
                                    <i class="bi bi-upload me-1"></i>
                                    استيراد
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-neumorphic">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الباركود</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>التكلفة</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- Products will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="صفحات المنتجات">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Product Modal -->
    <div class="modal fade modal-neumorphic" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="productForm" novalidate>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control neumorphic-input" id="productName" required>
                                <div class="invalid-feedback">يرجى إدخال اسم المنتج</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الباركود *</label>
                                <input type="text" class="form-control neumorphic-input" id="productBarcode" required>
                                <div class="invalid-feedback">يرجى إدخال الباركود</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الفئة *</label>
                                <select class="form-select neumorphic-input" id="productCategory" required>
                                    <option value="">اختر الفئة</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الفئة</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">السعر *</label>
                                <input type="number" class="form-control neumorphic-input" id="productPrice" step="0.01" min="0" required>
                                <div class="invalid-feedback">يرجى إدخال السعر</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">التكلفة</label>
                                <input type="number" class="form-control neumorphic-input" id="productCost" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">المخزون *</label>
                                <input type="number" class="form-control neumorphic-input" id="productStock" min="0" required>
                                <div class="invalid-feedback">يرجى إدخال كمية المخزون</div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control neumorphic-input" id="productDescription" rows="3"></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control neumorphic-input" id="productImage" accept="image/*">
                                <small class="text-muted">اختياري - يمكنك رفع صورة للمنتج</small>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="productActive" checked>
                                    <label class="form-check-label" for="productActive">
                                        منتج نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn neumorphic-btn-primary" id="saveProductBtn">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/products.js"></script>
</body>
</html>
