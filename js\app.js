// Main Application Logic for Arabic POS System

class POSApp {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.notifications = [];
        this.settings = Storage.getSettings();
        
        this.initializeApp();
    }
    
    initializeApp() {
        // Set up global event listeners
        this.setupGlobalEventListeners();
        
        // Initialize page-specific functionality
        this.initializePage();
        
        // Setup notifications
        this.setupNotifications();
        
        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();
    }
    
    getCurrentPage() {
        const path = window.location.pathname;
        const page = path.split('/').pop().split('.')[0];
        return page || 'index';
    }
    
    setupGlobalEventListeners() {
        // Handle form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle clicks
        document.addEventListener('click', this.handleClick.bind(this));
        
        // Handle input changes
        document.addEventListener('input', this.handleInput.bind(this));
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Handle before unload
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }
    
    initializePage() {
        switch (this.currentPage) {
            case 'dashboard':
                this.initializeDashboard();
                break;
            case 'pos':
                this.initializePOS();
                break;
            case 'products':
                this.initializeProducts();
                break;
            case 'customers':
                this.initializeCustomers();
                break;
            case 'reports':
                this.initializeReports();
                break;
        }
    }
    
    initializeDashboard() {
        // Dashboard-specific initialization
        this.updateDashboardStats();
        this.setupDashboardRefresh();
    }
    
    initializePOS() {
        // POS-specific initialization
        this.setupBarcodeScanner();
        this.setupPOSCalculations();
    }
    
    initializeProducts() {
        // Products-specific initialization
        this.setupProductSearch();
        this.setupProductFilters();
    }
    
    initializeCustomers() {
        // Customers-specific initialization
        this.setupCustomerSearch();
        this.setupCustomerValidation();
    }
    
    initializeReports() {
        // Reports-specific initialization
        this.setupDateRangePickers();
        this.setupCharts();
    }
    
    handleFormSubmit(event) {
        const form = event.target;
        
        // Add loading state
        this.setFormLoading(form, true);
        
        // Validate form
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            this.setFormLoading(form, false);
        }
        
        form.classList.add('was-validated');
    }
    
    handleClick(event) {
        const target = event.target;
        
        // Handle delete confirmations
        if (target.classList.contains('delete-btn')) {
            if (!confirm('هل أنت متأكد من الحذف؟')) {
                event.preventDefault();
            }
        }
        
        // Handle external links
        if (target.tagName === 'A' && target.href && target.target === '_blank') {
            // Log external link clicks
            this.logActivity('external_link', `زيارة رابط خارجي: ${target.href}`);
        }
    }
    
    handleInput(event) {
        const input = event.target;
        
        // Real-time validation
        if (input.hasAttribute('data-validate')) {
            this.validateInput(input);
        }
        
        // Auto-save for certain inputs
        if (input.hasAttribute('data-autosave')) {
            this.debounce(() => {
                this.autoSaveInput(input);
            }, 1000)();
        }
    }
    
    handleResize() {
        // Adjust layout for mobile
        this.adjustMobileLayout();
    }
    
    handleBeforeUnload(event) {
        // Check for unsaved changes
        if (this.hasUnsavedChanges()) {
            event.preventDefault();
            event.returnValue = 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟';
        }
    }
    
    // Utility functions
    setFormLoading(form, loading) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            if (loading) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'حفظ';
            }
        }
    }
    
    validateInput(input) {
        const validationType = input.getAttribute('data-validate');
        let isValid = true;
        let message = '';
        
        switch (validationType) {
            case 'email':
                isValid = this.validateEmail(input.value);
                message = 'يرجى إدخال بريد إلكتروني صحيح';
                break;
            case 'phone':
                isValid = this.validatePhone(input.value);
                message = 'يرجى إدخال رقم هاتف صحيح';
                break;
            case 'number':
                isValid = !isNaN(input.value) && input.value !== '';
                message = 'يرجى إدخال رقم صحيح';
                break;
            case 'barcode':
                isValid = this.validateBarcode(input.value);
                message = 'يرجى إدخال باركود صحيح';
                break;
        }
        
        this.setInputValidation(input, isValid, message);
    }
    
    setInputValidation(input, isValid, message) {
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            if (feedback) {
                feedback.textContent = message;
            }
        }
    }
    
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    validatePhone(phone) {
        const re = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return re.test(phone);
    }
    
    validateBarcode(barcode) {
        // Basic barcode validation (can be enhanced)
        return barcode.length >= 8 && /^\d+$/.test(barcode);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Notification system
    setupNotifications() {
        this.createNotificationContainer();
    }
    
    createNotificationContainer() {
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        const id = 'notification_' + Date.now();
        
        notification.id = id;
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.innerHTML = `
            <i class="bi bi-${this.getNotificationIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            const element = document.getElementById(id);
            if (element) {
                element.remove();
            }
        }, duration);
        
        return id;
    }
    
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + S for save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                this.handleSaveShortcut();
            }
            
            // Ctrl/Cmd + N for new
            if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
                event.preventDefault();
                this.handleNewShortcut();
            }
            
            // F1 for help
            if (event.key === 'F1') {
                event.preventDefault();
                this.showHelp();
            }
            
            // Escape to close modals
            if (event.key === 'Escape') {
                this.closeTopModal();
            }
        });
    }
    
    handleSaveShortcut() {
        const activeForm = document.querySelector('form:not([hidden])');
        if (activeForm) {
            const submitBtn = activeForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    }
    
    handleNewShortcut() {
        // Page-specific new actions
        switch (this.currentPage) {
            case 'products':
                this.showAddProductModal();
                break;
            case 'customers':
                this.showAddCustomerModal();
                break;
            case 'pos':
                this.clearCart();
                break;
        }
    }
    
    showHelp() {
        const helpContent = this.getHelpContent();
        this.showModal('مساعدة', helpContent);
    }
    
    getHelpContent() {
        const shortcuts = {
            'Ctrl+S': 'حفظ',
            'Ctrl+N': 'جديد',
            'F1': 'مساعدة',
            'Escape': 'إغلاق النوافذ المنبثقة'
        };
        
        let content = '<h5>اختصارات لوحة المفاتيح:</h5><ul>';
        for (const [key, description] of Object.entries(shortcuts)) {
            content += `<li><kbd>${key}</kbd> - ${description}</li>`;
        }
        content += '</ul>';
        
        return content;
    }
    
    closeTopModal() {
        const modals = document.querySelectorAll('.modal.show');
        if (modals.length > 0) {
            const topModal = modals[modals.length - 1];
            const bsModal = bootstrap.Modal.getInstance(topModal);
            if (bsModal) {
                bsModal.hide();
            }
        }
    }
    
    // Generic modal helper
    showModal(title, content, size = '') {
        const modalId = 'dynamicModal_' + Date.now();
        const modal = document.createElement('div');
        modal.className = 'modal fade modal-neumorphic';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog ${size}">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
        
        return bsModal;
    }
    
    // Data formatting helpers
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    }
    
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }
    
    formatDateTime(date) {
        return new Date(date).toLocaleString('ar-SA');
    }
    
    // Activity logging
    logActivity(action, description) {
        if (Auth.getCurrentUser()) {
            Auth.logActivity(action, description);
        }
    }
    
    // Check for unsaved changes
    hasUnsavedChanges() {
        const forms = document.querySelectorAll('form[data-track-changes]');
        for (const form of forms) {
            if (form.hasAttribute('data-changed')) {
                return true;
            }
        }
        return false;
    }
    
    // Mobile layout adjustments
    adjustMobileLayout() {
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile-layout', isMobile);
    }
    
    // Auto-save functionality
    autoSaveInput(input) {
        const key = `autosave_${input.name || input.id}`;
        localStorage.setItem(key, input.value);
    }
    
    restoreAutoSavedInputs() {
        const inputs = document.querySelectorAll('[data-autosave]');
        inputs.forEach(input => {
            const key = `autosave_${input.name || input.id}`;
            const savedValue = localStorage.getItem(key);
            if (savedValue) {
                input.value = savedValue;
            }
        });
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.POSApp = new POSApp();
});
