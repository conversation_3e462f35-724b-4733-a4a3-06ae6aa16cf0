<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-neumorphic">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <a class="navbar-brand text-gradient-primary fw-bold" href="dashboard.html">
                <i class="bi bi-shop me-2"></i>
                نظام نقاط البيع
            </a>
            
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn neumorphic-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="currentUserName">المدير</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showProfileModal()">
                            <i class="bi bi-person me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettingsModal()">
                            <i class="bi bi-gear me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar-neumorphic" id="sidebar">
        <div class="text-center mb-4">
            <div class="logo-container mx-auto mb-3">
                <i class="bi bi-shop text-primary"></i>
            </div>
            <h5 class="text-gradient-primary">نقاط البيع</h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="sidebar-item active" href="dashboard.html">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            <a class="sidebar-item" href="pos.html">
                <i class="bi bi-cash-register"></i>
                نقطة البيع
            </a>
            <a class="sidebar-item" href="products.html">
                <i class="bi bi-box-seam"></i>
                إدارة المنتجات
            </a>
            <a class="sidebar-item" href="customers.html">
                <i class="bi bi-people"></i>
                إدارة العملاء
            </a>
            <a class="sidebar-item" href="reports.html">
                <i class="bi bi-graph-up"></i>
                التقارير
            </a>
            <a class="sidebar-item" href="#" onclick="showBackupModal()">
                <i class="bi bi-cloud-download"></i>
                النسخ الاحتياطي
            </a>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid p-4">
            <!-- Welcome Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card fade-in">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="text-gradient-primary mb-2">مرحباً بك، <span id="welcomeUserName">المدير</span></h2>
                                <p class="text-muted mb-0">إليك ملخص أداء متجرك اليوم</p>
                            </div>
                            <div class="text-end">
                                <p class="mb-1 text-muted">التاريخ</p>
                                <h5 class="mb-0" id="currentDate"></h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card slide-in-right">
                    <div class="stat-icon bg-gradient-primary">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stat-value" id="todaySales">0</div>
                    <div class="stat-label">مبيعات اليوم</div>
                </div>
                
                <div class="stat-card slide-in-right" style="animation-delay: 0.1s;">
                    <div class="stat-icon bg-gradient-success">
                        <i class="bi bi-receipt"></i>
                    </div>
                    <div class="stat-value" id="todayOrders">0</div>
                    <div class="stat-label">طلبات اليوم</div>
                </div>
                
                <div class="stat-card slide-in-right" style="animation-delay: 0.2s;">
                    <div class="stat-icon bg-gradient-warning">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <div class="stat-value" id="totalProducts">0</div>
                    <div class="stat-label">إجمالي المنتجات</div>
                </div>
                
                <div class="stat-card slide-in-right" style="animation-delay: 0.3s;">
                    <div class="stat-icon bg-gradient-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value" id="lowStockItems">0</div>
                    <div class="stat-label">منتجات قليلة المخزون</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <h4 class="mb-3">إجراءات سريعة</h4>
                        <div class="row g-3">
                            <div class="col-md-3 col-sm-6">
                                <a href="pos.html" class="btn neumorphic-btn-primary w-100 p-3">
                                    <i class="bi bi-cash-register d-block mb-2" style="font-size: 2rem;"></i>
                                    بدء البيع
                                </a>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <button class="btn neumorphic-btn-secondary w-100 p-3" onclick="showAddProductModal()">
                                    <i class="bi bi-plus-circle d-block mb-2" style="font-size: 2rem;"></i>
                                    إضافة منتج
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <button class="btn neumorphic-btn-success w-100 p-3" onclick="showAddCustomerModal()">
                                    <i class="bi bi-person-plus d-block mb-2" style="font-size: 2rem;"></i>
                                    إضافة عميل
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <a href="reports.html" class="btn neumorphic-btn-secondary w-100 p-3">
                                    <i class="bi bi-graph-up d-block mb-2" style="font-size: 2rem;"></i>
                                    عرض التقارير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Sales and Low Stock -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="neumorphic-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>آخر المبيعات</h4>
                            <a href="reports.html" class="btn neumorphic-btn-secondary btn-sm">
                                عرض الكل
                            </a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-neumorphic">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody id="recentSalesTable">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">لا توجد مبيعات حديثة</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="neumorphic-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>تنبيهات المخزون</h4>
                            <span class="badge bg-gradient-danger" id="lowStockBadge">0</span>
                        </div>
                        <div id="lowStockList">
                            <p class="text-muted text-center">لا توجد تنبيهات مخزون</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script>
        // Dashboard specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!Auth.requireAuth()) {
                return;
            }
            
            // Initialize dashboard
            initializeDashboard();
            
            // Setup mobile menu
            setupMobileMenu();
            
            // Load dashboard data
            loadDashboardData();
            
            // Update current date
            updateCurrentDate();
            
            // Set user name
            const currentUser = Auth.getCurrentUser();
            if (currentUser) {
                document.getElementById('currentUserName').textContent = currentUser.name;
                document.getElementById('welcomeUserName').textContent = currentUser.name;
            }
        });
        
        function initializeDashboard() {
            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.neumorphic-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        }
        
        function setupMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            
            mobileMenuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            });
            
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });
        }
        
        function loadDashboardData() {
            // Load statistics
            loadStatistics();
            
            // Load recent sales
            loadRecentSales();
            
            // Load low stock alerts
            loadLowStockAlerts();
        }
        
        function loadStatistics() {
            const sales = Storage.getSales();
            const products = Storage.getProducts();
            const settings = Storage.getSettings();
            
            // Today's sales
            const today = new Date().toDateString();
            const todaySales = sales.filter(sale => 
                new Date(sale.createdAt).toDateString() === today
            );
            
            const todayTotal = todaySales.reduce((sum, sale) => sum + sale.total, 0);
            const todayOrdersCount = todaySales.length;
            
            // Low stock items
            const lowStockThreshold = settings?.lowStockAlert || 10;
            const lowStockItems = products.filter(product => 
                product.stock <= lowStockThreshold && product.isActive
            );
            
            // Update UI
            document.getElementById('todaySales').textContent = todayTotal.toFixed(2) + ' ر.س';
            document.getElementById('todayOrders').textContent = todayOrdersCount;
            document.getElementById('totalProducts').textContent = products.filter(p => p.isActive).length;
            document.getElementById('lowStockItems').textContent = lowStockItems.length;
        }
        
        function loadRecentSales() {
            const sales = Storage.getSales();
            const customers = Storage.getCustomers();
            const recentSales = sales.slice(-5).reverse();
            
            const tableBody = document.getElementById('recentSalesTable');
            
            if (recentSales.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">لا توجد مبيعات حديثة</td></tr>';
                return;
            }
            
            tableBody.innerHTML = recentSales.map(sale => {
                const customer = customers.find(c => c.id === sale.customerId);
                const customerName = customer ? customer.name : 'عميل نقدي';
                const date = new Date(sale.createdAt).toLocaleDateString('ar-SA');
                
                return `
                    <tr>
                        <td>#${sale.id}</td>
                        <td>${customerName}</td>
                        <td>${sale.total.toFixed(2)} ر.س</td>
                        <td>${date}</td>
                    </tr>
                `;
            }).join('');
        }
        
        function loadLowStockAlerts() {
            const products = Storage.getProducts();
            const settings = Storage.getSettings();
            const lowStockThreshold = settings?.lowStockAlert || 10;
            
            const lowStockItems = products.filter(product => 
                product.stock <= lowStockThreshold && product.isActive
            );
            
            const lowStockList = document.getElementById('lowStockList');
            const lowStockBadge = document.getElementById('lowStockBadge');
            
            lowStockBadge.textContent = lowStockItems.length;
            
            if (lowStockItems.length === 0) {
                lowStockList.innerHTML = '<p class="text-muted text-center">لا توجد تنبيهات مخزون</p>';
                return;
            }
            
            lowStockList.innerHTML = lowStockItems.map(product => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                    <div>
                        <strong>${product.name}</strong>
                        <br>
                        <small class="text-muted">المخزون: ${product.stock}</small>
                    </div>
                    <span class="badge bg-gradient-danger">${product.stock}</span>
                </div>
            `).join('');
        }
        
        function updateCurrentDate() {
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            document.getElementById('currentDate').textContent = 
                now.toLocaleDateString('ar-SA', options);
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                Auth.logout();
            }
        }
        
        // Placeholder functions for modals (to be implemented)
        function showProfileModal() {
            alert('سيتم تطوير هذه الميزة قريباً');
        }
        
        function showSettingsModal() {
            alert('سيتم تطوير هذه الميزة قريباً');
        }
        
        function showAddProductModal() {
            window.location.href = 'products.html';
        }
        
        function showAddCustomerModal() {
            window.location.href = 'customers.html';
        }
        
        function showBackupModal() {
            alert('سيتم تطوير هذه الميزة قريباً');
        }
    </script>
</body>
</html>
