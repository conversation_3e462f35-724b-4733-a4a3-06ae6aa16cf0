<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-neumorphic">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <a class="navbar-brand text-gradient-primary fw-bold" href="dashboard.html">
                <i class="bi bi-people me-2"></i>
                إدارة العملاء
            </a>
            
            <div class="d-flex align-items-center">
                <button class="btn neumorphic-btn-primary me-2" onclick="showAddCustomerModal()">
                    <i class="bi bi-person-plus me-1"></i>
                    إضافة عميل
                </button>
                <div class="dropdown">
                    <button class="btn neumorphic-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="currentUserName">المدير</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar-neumorphic" id="sidebar">
        <div class="text-center mb-4">
            <div class="logo-container mx-auto mb-3">
                <i class="bi bi-people text-primary"></i>
            </div>
            <h5 class="text-gradient-primary">إدارة العملاء</h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="sidebar-item" href="dashboard.html">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            <a class="sidebar-item" href="pos.html">
                <i class="bi bi-cash-register"></i>
                نقطة البيع
            </a>
            <a class="sidebar-item" href="products.html">
                <i class="bi bi-box-seam"></i>
                إدارة المنتجات
            </a>
            <a class="sidebar-item active" href="customers.html">
                <i class="bi bi-people"></i>
                إدارة العملاء
            </a>
            <a class="sidebar-item" href="reports.html">
                <i class="bi bi-graph-up"></i>
                التقارير
            </a>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid p-4">
            <!-- Search Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <input type="text" class="form-control neumorphic-input" id="searchInput" 
                                           placeholder="ابحث بالاسم أو الهاتف أو البريد الإلكتروني...">
                                    <button class="btn neumorphic-btn-primary" onclick="searchCustomers()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">نوع العميل</label>
                                <select class="form-select neumorphic-input" id="typeFilter" onchange="filterCustomers()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual">فرد</option>
                                    <option value="company">شركة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn neumorphic-btn-secondary" onclick="resetFilters()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-primary">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-value" id="totalCustomers">0</div>
                        <div class="stat-label">إجمالي العملاء</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-success">
                            <i class="bi bi-person-check"></i>
                        </div>
                        <div class="stat-value" id="activeCustomers">0</div>
                        <div class="stat-label">عملاء نشطون</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-info">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="stat-value" id="companyCustomers">0</div>
                        <div class="stat-label">عملاء شركات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-warning">
                            <i class="bi bi-calendar-week"></i>
                        </div>
                        <div class="stat-value" id="newCustomers">0</div>
                        <div class="stat-label">عملاء جدد هذا الشهر</div>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="row">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>قائمة العملاء</h4>
                            <div class="btn-group">
                                <button class="btn neumorphic-btn-primary" onclick="showAddCustomerModal()">
                                    <i class="bi bi-person-plus me-1"></i>
                                    إضافة عميل
                                </button>
                                <button class="btn neumorphic-btn-secondary" onclick="exportCustomers()">
                                    <i class="bi bi-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-neumorphic">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>النوع</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="customersTableBody">
                                    <!-- Customers will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="صفحات العملاء">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Customer Modal -->
    <div class="modal fade modal-neumorphic" id="customerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerModalTitle">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="customerForm" novalidate>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control neumorphic-input" id="customerName" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نوع العميل *</label>
                                <select class="form-select neumorphic-input" id="customerType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="individual">فرد</option>
                                    <option value="company">شركة</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار نوع العميل</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control neumorphic-input" id="customerPhone" 
                                       data-validate="phone" required>
                                <div class="invalid-feedback">يرجى إدخال رقم هاتف صحيح</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control neumorphic-input" id="customerEmail" 
                                       data-validate="email">
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control neumorphic-input" id="customerAddress" rows="3"></textarea>
                            </div>
                            <div class="col-md-6" id="companyFields" style="display: none;">
                                <label class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control neumorphic-input" id="customerTaxNumber">
                            </div>
                            <div class="col-md-6" id="companyFields2" style="display: none;">
                                <label class="form-label">الرقم التجاري</label>
                                <input type="text" class="form-control neumorphic-input" id="customerCommercialNumber">
                            </div>
                            <div class="col-12">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control neumorphic-input" id="customerNotes" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn neumorphic-btn-primary" id="saveCustomerBtn">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div class="modal fade modal-neumorphic" id="customerDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل العميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="customerDetailsContent">
                    <!-- Customer details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/customers.js"></script>
</body>
</html>
