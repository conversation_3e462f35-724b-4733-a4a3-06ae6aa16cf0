// Storage Management for Arabic POS System
// Handles localStorage operations with data validation and backup

class StorageManager {
    constructor() {
        this.storageKeys = {
            products: 'pos_products',
            customers: 'pos_customers',
            sales: 'pos_sales',
            expenses: 'pos_expenses',
            settings: 'pos_settings',
            users: 'pos_users',
            categories: 'pos_categories',
            currentUser: 'pos_current_user',
            sessionData: 'pos_session'
        };
        
        this.initializeDefaultData();
    }
    
    // Initialize default data if not exists
    initializeDefaultData() {
        // Initialize default admin user
        if (!this.getUsers().length) {
            this.saveUsers([{
                id: 1,
                username: 'admin',
                password: 'admin123', // In production, this should be hashed
                role: 'admin',
                name: 'المدير العام',
                email: '<EMAIL>',
                createdAt: new Date().toISOString(),
                isActive: true
            }]);
        }
        
        // Initialize default categories
        if (!this.getCategories().length) {
            this.saveCategories([
                { id: 1, name: 'مواد غذائية', description: 'المواد الغذائية والمشروبات', color: '#28a745' },
                { id: 2, name: 'إلكترونيات', description: 'الأجهزة الإلكترونية والكهربائية', color: '#007bff' },
                { id: 3, name: 'ملابس', description: 'الملابس والأزياء', color: '#6f42c1' },
                { id: 4, name: 'منزلية', description: 'الأدوات المنزلية والديكور', color: '#fd7e14' },
                { id: 5, name: 'أخرى', description: 'منتجات متنوعة', color: '#6c757d' }
            ]);
        }
        
        // Initialize sample products if none exist
        if (!this.getProducts().length) {
            this.saveProducts([
                {
                    id: 1,
                    name: 'خبز أبيض',
                    barcode: '1234567890123',
                    price: 2.50,
                    cost: 1.50,
                    stock: 100,
                    category: 'مواد غذائية',
                    categoryId: 1,
                    description: 'خبز أبيض طازج',
                    image: '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isActive: true
                },
                {
                    id: 2,
                    name: 'حليب كامل الدسم',
                    barcode: '2345678901234',
                    price: 5.00,
                    cost: 3.50,
                    stock: 50,
                    category: 'مواد غذائية',
                    categoryId: 1,
                    description: 'حليب طازج كامل الدسم 1 لتر',
                    image: '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isActive: true
                },
                {
                    id: 3,
                    name: 'قميص قطني',
                    barcode: '3456789012345',
                    price: 45.00,
                    cost: 25.00,
                    stock: 20,
                    category: 'ملابس',
                    categoryId: 3,
                    description: 'قميص قطني عالي الجودة',
                    image: '',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isActive: true
                }
            ]);
        }
        
        // Initialize settings
        if (!this.getSettings()) {
            this.saveSettings({
                storeName: 'متجر نقاط البيع',
                storeAddress: 'الرياض، المملكة العربية السعودية',
                storePhone: '+966 11 123 4567',
                storeEmail: '<EMAIL>',
                currency: 'ريال سعودي',
                currencySymbol: 'ر.س',
                taxRate: 15,
                receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                lowStockAlert: 10,
                autoBackup: true,
                printAfterSale: true,
                language: 'ar',
                theme: 'light'
            });
        }
    }
    
    // Generic storage methods
    setItem(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }
    
    getItem(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }
    
    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }
    
    // Products management
    getProducts() {
        return this.getItem(this.storageKeys.products) || [];
    }
    
    saveProducts(products) {
        return this.setItem(this.storageKeys.products, products);
    }
    
    addProduct(product) {
        const products = this.getProducts();
        const newId = Math.max(...products.map(p => p.id), 0) + 1;
        product.id = newId;
        product.createdAt = new Date().toISOString();
        product.updatedAt = new Date().toISOString();
        products.push(product);
        return this.saveProducts(products);
    }
    
    updateProduct(productId, updatedProduct) {
        const products = this.getProducts();
        const index = products.findIndex(p => p.id === productId);
        if (index !== -1) {
            products[index] = { ...products[index], ...updatedProduct, updatedAt: new Date().toISOString() };
            return this.saveProducts(products);
        }
        return false;
    }
    
    deleteProduct(productId) {
        const products = this.getProducts();
        const filteredProducts = products.filter(p => p.id !== productId);
        return this.saveProducts(filteredProducts);
    }
    
    getProductByBarcode(barcode) {
        const products = this.getProducts();
        return products.find(p => p.barcode === barcode && p.isActive);
    }
    
    // Customers management
    getCustomers() {
        return this.getItem(this.storageKeys.customers) || [];
    }
    
    saveCustomers(customers) {
        return this.setItem(this.storageKeys.customers, customers);
    }
    
    addCustomer(customer) {
        const customers = this.getCustomers();
        const newId = Math.max(...customers.map(c => c.id), 0) + 1;
        customer.id = newId;
        customer.createdAt = new Date().toISOString();
        customer.updatedAt = new Date().toISOString();
        customers.push(customer);
        return this.saveCustomers(customers);
    }
    
    updateCustomer(customerId, updatedCustomer) {
        const customers = this.getCustomers();
        const index = customers.findIndex(c => c.id === customerId);
        if (index !== -1) {
            customers[index] = { ...customers[index], ...updatedCustomer, updatedAt: new Date().toISOString() };
            return this.saveCustomers(customers);
        }
        return false;
    }
    
    deleteCustomer(customerId) {
        const customers = this.getCustomers();
        const filteredCustomers = customers.filter(c => c.id !== customerId);
        return this.saveCustomers(filteredCustomers);
    }
    
    // Sales management
    getSales() {
        return this.getItem(this.storageKeys.sales) || [];
    }
    
    saveSales(sales) {
        return this.setItem(this.storageKeys.sales, sales);
    }
    
    addSale(sale) {
        const sales = this.getSales();
        const newId = Math.max(...sales.map(s => s.id), 0) + 1;
        sale.id = newId;
        sale.createdAt = new Date().toISOString();
        sales.push(sale);
        
        // Update product stock
        sale.items.forEach(item => {
            this.updateProductStock(item.productId, -item.quantity);
        });
        
        return this.saveSales(sales);
    }
    
    updateProductStock(productId, quantityChange) {
        const products = this.getProducts();
        const product = products.find(p => p.id === productId);
        if (product) {
            product.stock += quantityChange;
            product.updatedAt = new Date().toISOString();
            this.saveProducts(products);
        }
    }
    
    // Categories management
    getCategories() {
        return this.getItem(this.storageKeys.categories) || [];
    }
    
    saveCategories(categories) {
        return this.setItem(this.storageKeys.categories, categories);
    }
    
    // Users management
    getUsers() {
        return this.getItem(this.storageKeys.users) || [];
    }
    
    saveUsers(users) {
        return this.setItem(this.storageKeys.users, users);
    }
    
    getUserByCredentials(username, password) {
        const users = this.getUsers();
        return users.find(u => u.username === username && u.password === password && u.isActive);
    }
    
    // Settings management
    getSettings() {
        return this.getItem(this.storageKeys.settings);
    }
    
    saveSettings(settings) {
        return this.setItem(this.storageKeys.settings, settings);
    }
    
    // Session management
    getCurrentUser() {
        return this.getItem(this.storageKeys.currentUser);
    }
    
    setCurrentUser(user) {
        return this.setItem(this.storageKeys.currentUser, user);
    }
    
    clearCurrentUser() {
        return this.removeItem(this.storageKeys.currentUser);
    }
    
    // Data export/import
    exportData() {
        const data = {
            products: this.getProducts(),
            customers: this.getCustomers(),
            sales: this.getSales(),
            categories: this.getCategories(),
            settings: this.getSettings(),
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        return JSON.stringify(data, null, 2);
    }
    
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.products) this.saveProducts(data.products);
            if (data.customers) this.saveCustomers(data.customers);
            if (data.sales) this.saveSales(data.sales);
            if (data.categories) this.saveCategories(data.categories);
            if (data.settings) this.saveSettings(data.settings);
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }
    
    // Clear all data
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            this.removeItem(key);
        });
        this.initializeDefaultData();
    }
    
    // Get storage usage
    getStorageUsage() {
        let totalSize = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                totalSize += localStorage[key].length;
            }
        }
        return {
            used: totalSize,
            usedMB: (totalSize / 1024 / 1024).toFixed(2),
            available: 5 * 1024 * 1024 - totalSize, // Assuming 5MB limit
            availableMB: ((5 * 1024 * 1024 - totalSize) / 1024 / 1024).toFixed(2)
        };
    }
}

// Create global instance
const Storage = new StorageManager();
