// Customers Management for Arabic POS System

class CustomersManager {
    constructor() {
        this.customers = [];
        this.sales = [];
        this.filteredCustomers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.editingCustomerId = null;
        
        this.initializeCustomers();
    }
    
    initializeCustomers() {
        // Check authentication
        if (!Auth.requireAuth()) {
            return;
        }
        
        // Load data
        this.loadData();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Load customers table
        this.loadCustomersTable();
        
        // Update statistics
        this.updateStatistics();
        
        // Setup mobile menu
        this.setupMobileMenu();
        
        // Set user name
        this.setUserName();
    }
    
    loadData() {
        this.customers = Storage.getCustomers();
        this.sales = Storage.getSales();
        this.filteredCustomers = [...this.customers];
    }
    
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', this.debounce(() => {
            this.searchCustomers();
        }, 300));
        
        // Customer form submission
        const customerForm = document.getElementById('customerForm');
        customerForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomer();
        });
        
        // Customer type change
        const customerType = document.getElementById('customerType');
        customerType.addEventListener('change', this.toggleCompanyFields.bind(this));
        
        // Phone validation
        const customerPhone = document.getElementById('customerPhone');
        customerPhone.addEventListener('input', this.formatPhoneNumber.bind(this));
    }
    
    setupMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
        });
        
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        });
    }
    
    setUserName() {
        const currentUser = Auth.getCurrentUser();
        if (currentUser) {
            document.getElementById('currentUserName').textContent = currentUser.name;
        }
    }
    
    toggleCompanyFields() {
        const customerType = document.getElementById('customerType').value;
        const companyFields = document.getElementById('companyFields');
        const companyFields2 = document.getElementById('companyFields2');
        
        if (customerType === 'company') {
            companyFields.style.display = 'block';
            companyFields2.style.display = 'block';
        } else {
            companyFields.style.display = 'none';
            companyFields2.style.display = 'none';
        }
    }
    
    formatPhoneNumber(event) {
        let value = event.target.value.replace(/\D/g, '');
        
        // Format Saudi phone numbers
        if (value.startsWith('966')) {
            value = '+966 ' + value.slice(3);
        } else if (value.startsWith('05')) {
            value = '+966 ' + value.slice(1);
        } else if (value.length === 9 && value.startsWith('5')) {
            value = '+966 ' + value;
        }
        
        event.target.value = value;
    }
    
    loadCustomersTable() {
        const tableBody = document.getElementById('customersTableBody');
        
        if (this.filteredCustomers.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="bi bi-people display-4"></i>
                        <p class="mt-2">لا توجد عملاء</p>
                        <button class="btn neumorphic-btn-primary" onclick="showAddCustomerModal()">
                            إضافة عميل جديد
                        </button>
                    </td>
                </tr>
            `;
            return;
        }
        
        // Pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedCustomers = this.filteredCustomers.slice(startIndex, endIndex);
        
        tableBody.innerHTML = paginatedCustomers.map(customer => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="customer-avatar me-2">
                            <i class="bi bi-${customer.type === 'company' ? 'building' : 'person-circle'} text-primary"></i>
                        </div>
                        <div>
                            <strong>${customer.name}</strong>
                            ${customer.notes ? `<br><small class="text-muted">${customer.notes}</small>` : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge ${customer.type === 'company' ? 'bg-gradient-info' : 'bg-gradient-primary'}">
                        ${customer.type === 'company' ? 'شركة' : 'فرد'}
                    </span>
                </td>
                <td>
                    <a href="tel:${customer.phone}" class="text-decoration-none">
                        <i class="bi bi-telephone me-1"></i>
                        ${customer.phone}
                    </a>
                </td>
                <td>
                    ${customer.email ? 
                        `<a href="mailto:${customer.email}" class="text-decoration-none">
                            <i class="bi bi-envelope me-1"></i>
                            ${customer.email}
                        </a>` : 
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    <small class="text-muted">
                        ${customer.address ? customer.address.substring(0, 50) + (customer.address.length > 50 ? '...' : '') : '-'}
                    </small>
                </td>
                <td>
                    <small class="text-muted">
                        ${new Date(customer.createdAt).toLocaleDateString('ar-SA')}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn neumorphic-btn-secondary" onclick="viewCustomerDetails(${customer.id})" title="عرض">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn neumorphic-btn-secondary" onclick="editCustomer(${customer.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn neumorphic-btn-danger delete-btn" onclick="deleteCustomer(${customer.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        this.updatePagination();
    }
    
    updatePagination() {
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link neumorphic-btn-secondary" href="#" onclick="changePage(${this.currentPage - 1})">السابق</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage || i === 1 || i === totalPages || (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link neumorphic-btn-secondary" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link neumorphic-btn-secondary" href="#" onclick="changePage(${this.currentPage + 1})">التالي</a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    updateStatistics() {
        const totalCustomers = this.customers.length;
        const companyCustomers = this.customers.filter(c => c.type === 'company').length;
        
        // Calculate new customers this month
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);
        const newCustomers = this.customers.filter(c => 
            new Date(c.createdAt) >= thisMonth
        ).length;
        
        // Calculate active customers (customers with sales in last 3 months)
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        const recentSales = this.sales.filter(s => 
            new Date(s.createdAt) >= threeMonthsAgo && s.customerId
        );
        const activeCustomerIds = [...new Set(recentSales.map(s => s.customerId))];
        const activeCustomers = activeCustomerIds.length;
        
        document.getElementById('totalCustomers').textContent = totalCustomers;
        document.getElementById('activeCustomers').textContent = activeCustomers;
        document.getElementById('companyCustomers').textContent = companyCustomers;
        document.getElementById('newCustomers').textContent = newCustomers;
    }
    
    searchCustomers() {
        const query = document.getElementById('searchInput').value.toLowerCase().trim();
        
        if (!query) {
            this.filteredCustomers = [...this.customers];
        } else {
            this.filteredCustomers = this.customers.filter(customer =>
                customer.name.toLowerCase().includes(query) ||
                customer.phone.includes(query) ||
                (customer.email && customer.email.toLowerCase().includes(query)) ||
                (customer.address && customer.address.toLowerCase().includes(query))
            );
        }
        
        this.currentPage = 1;
        this.loadCustomersTable();
    }
    
    filterCustomers() {
        const typeFilter = document.getElementById('typeFilter').value;
        const searchQuery = document.getElementById('searchInput').value.toLowerCase().trim();
        
        this.filteredCustomers = this.customers.filter(customer => {
            // Type filter
            if (typeFilter && customer.type !== typeFilter) {
                return false;
            }
            
            // Search filter
            if (searchQuery) {
                if (!customer.name.toLowerCase().includes(searchQuery) &&
                    !customer.phone.includes(searchQuery) &&
                    !(customer.email && customer.email.toLowerCase().includes(searchQuery)) &&
                    !(customer.address && customer.address.toLowerCase().includes(searchQuery))) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.currentPage = 1;
        this.loadCustomersTable();
    }
    
    resetFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        
        this.filteredCustomers = [...this.customers];
        this.currentPage = 1;
        this.loadCustomersTable();
    }
    
    showAddCustomerModal() {
        this.editingCustomerId = null;
        document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
        document.getElementById('saveCustomerBtn').innerHTML = '<i class="bi bi-check-circle me-2"></i>حفظ العميل';
        
        // Reset form
        document.getElementById('customerForm').reset();
        this.toggleCompanyFields();
        
        const modal = new bootstrap.Modal(document.getElementById('customerModal'));
        modal.show();
    }
    
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) {
            this.showNotification('العميل غير موجود', 'danger');
            return;
        }
        
        this.editingCustomerId = customerId;
        document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
        document.getElementById('saveCustomerBtn').innerHTML = '<i class="bi bi-check-circle me-2"></i>حفظ التغييرات';
        
        // Fill form with customer data
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerType').value = customer.type;
        document.getElementById('customerPhone').value = customer.phone;
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerTaxNumber').value = customer.taxNumber || '';
        document.getElementById('customerCommercialNumber').value = customer.commercialNumber || '';
        document.getElementById('customerNotes').value = customer.notes || '';
        
        this.toggleCompanyFields();
        
        const modal = new bootstrap.Modal(document.getElementById('customerModal'));
        modal.show();
    }
    
    saveCustomer() {
        const form = document.getElementById('customerForm');
        
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }
        
        const customerData = {
            name: document.getElementById('customerName').value.trim(),
            type: document.getElementById('customerType').value,
            phone: document.getElementById('customerPhone').value.trim(),
            email: document.getElementById('customerEmail').value.trim() || null,
            address: document.getElementById('customerAddress').value.trim() || null,
            notes: document.getElementById('customerNotes').value.trim() || null
        };
        
        // Add company-specific fields
        if (customerData.type === 'company') {
            customerData.taxNumber = document.getElementById('customerTaxNumber').value.trim() || null;
            customerData.commercialNumber = document.getElementById('customerCommercialNumber').value.trim() || null;
        }
        
        // Validate phone uniqueness
        const existingCustomer = this.customers.find(c => 
            c.phone === customerData.phone && c.id !== this.editingCustomerId
        );
        
        if (existingCustomer) {
            this.showNotification('رقم الهاتف موجود مسبقاً', 'danger');
            return;
        }
        
        let success = false;
        
        if (this.editingCustomerId) {
            // Update existing customer
            success = Storage.updateCustomer(this.editingCustomerId, customerData);
            if (success) {
                this.showNotification('تم تحديث العميل بنجاح', 'success');
                Auth.logActivity('customer_updated', `تحديث العميل: ${customerData.name}`);
            }
        } else {
            // Add new customer
            success = Storage.addCustomer(customerData);
            if (success) {
                this.showNotification('تم إضافة العميل بنجاح', 'success');
                Auth.logActivity('customer_added', `إضافة عميل جديد: ${customerData.name}`);
            }
        }
        
        if (success) {
            // Reload data and update UI
            this.loadData();
            this.loadCustomersTable();
            this.updateStatistics();
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('customerModal'));
            modal.hide();
        } else {
            this.showNotification('حدث خطأ أثناء حفظ العميل', 'danger');
        }
    }
    
    deleteCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) {
            this.showNotification('العميل غير موجود', 'danger');
            return;
        }
        
        // Check if customer has sales
        const customerSales = this.sales.filter(s => s.customerId === customerId);
        
        let confirmMessage = `هل أنت متأكد من حذف العميل "${customer.name}"؟`;
        if (customerSales.length > 0) {
            confirmMessage += `\n\nتحذير: هذا العميل لديه ${customerSales.length} عملية شراء. سيتم الاحتفاظ بسجل المبيعات ولكن سيتم إزالة ربطها بالعميل.`;
        }
        confirmMessage += '\n\nهذا الإجراء لا يمكن التراجع عنه.';
        
        if (confirm(confirmMessage)) {
            if (Storage.deleteCustomer(customerId)) {
                this.showNotification('تم حذف العميل بنجاح', 'success');
                Auth.logActivity('customer_deleted', `حذف العميل: ${customer.name}`);
                
                // Reload data and update UI
                this.loadData();
                this.loadCustomersTable();
                this.updateStatistics();
            } else {
                this.showNotification('حدث خطأ أثناء حذف العميل', 'danger');
            }
        }
    }
    
    viewCustomerDetails(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) {
            this.showNotification('العميل غير موجود', 'danger');
            return;
        }
        
        // Get customer sales
        const customerSales = this.sales.filter(s => s.customerId === customerId);
        const totalPurchases = customerSales.reduce((sum, sale) => sum + sale.total, 0);
        const lastPurchase = customerSales.length > 0 ? 
            new Date(Math.max(...customerSales.map(s => new Date(s.createdAt)))).toLocaleDateString('ar-SA') : 
            'لا توجد مشتريات';
        
        const detailsContent = document.getElementById('customerDetailsContent');
        detailsContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">المعلومات الأساسية</h6>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>${customer.name}</td>
                        </tr>
                        <tr>
                            <td><strong>النوع:</strong></td>
                            <td>
                                <span class="badge ${customer.type === 'company' ? 'bg-gradient-info' : 'bg-gradient-primary'}">
                                    ${customer.type === 'company' ? 'شركة' : 'فرد'}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td><a href="tel:${customer.phone}">${customer.phone}</a></td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td>${customer.email ? `<a href="mailto:${customer.email}">${customer.email}</a>` : '-'}</td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>${customer.address || '-'}</td>
                        </tr>
                        ${customer.type === 'company' ? `
                            <tr>
                                <td><strong>الرقم الضريبي:</strong></td>
                                <td>${customer.taxNumber || '-'}</td>
                            </tr>
                            <tr>
                                <td><strong>الرقم التجاري:</strong></td>
                                <td>${customer.commercialNumber || '-'}</td>
                            </tr>
                        ` : ''}
                        <tr>
                            <td><strong>تاريخ التسجيل:</strong></td>
                            <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">إحصائيات المشتريات</h6>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>عدد المشتريات:</strong></td>
                            <td>${customerSales.length}</td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي المشتريات:</strong></td>
                            <td>${totalPurchases.toFixed(2)} ر.س</td>
                        </tr>
                        <tr>
                            <td><strong>آخر شراء:</strong></td>
                            <td>${lastPurchase}</td>
                        </tr>
                    </table>
                    
                    ${customer.notes ? `
                        <h6 class="text-primary mt-3">ملاحظات</h6>
                        <p class="text-muted">${customer.notes}</p>
                    ` : ''}
                </div>
            </div>
            
            ${customerSales.length > 0 ? `
                <hr>
                <h6 class="text-primary">آخر المشتريات</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customerSales.slice(-5).reverse().map(sale => `
                                <tr>
                                    <td>#${sale.id}</td>
                                    <td>${new Date(sale.createdAt).toLocaleDateString('ar-SA')}</td>
                                    <td>${sale.total.toFixed(2)} ر.س</td>
                                    <td>${this.getPaymentMethodName(sale.paymentMethod)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            ` : ''}
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
        modal.show();
    }
    
    getPaymentMethodName(method) {
        const methods = {
            'cash': 'نقدي',
            'card': 'بطاقة ائتمان',
            'transfer': 'تحويل بنكي'
        };
        return methods[method] || method;
    }
    
    exportCustomers() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `customers_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showNotification('تم تصدير العملاء بنجاح', 'success');
        }
    }
    
    generateCSV() {
        const headers = ['الاسم', 'النوع', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرقم الضريبي', 'الرقم التجاري', 'ملاحظات', 'تاريخ التسجيل'];
        const rows = this.customers.map(customer => [
            customer.name,
            customer.type === 'company' ? 'شركة' : 'فرد',
            customer.phone,
            customer.email || '',
            customer.address || '',
            customer.taxNumber || '',
            customer.commercialNumber || '',
            customer.notes || '',
            new Date(customer.createdAt).toLocaleDateString('ar-SA')
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }
    
    changePage(page) {
        const totalPages = Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        
        if (page < 1 || page > totalPages) {
            return;
        }
        
        this.currentPage = page;
        this.loadCustomersTable();
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    showNotification(message, type = 'info', duration = 3000) {
        if (window.POSApp) {
            window.POSApp.showNotification(message, type, duration);
        } else {
            alert(message);
        }
    }
}

// Global functions for HTML onclick events
function showAddCustomerModal() {
    customersManager.showAddCustomerModal();
}

function editCustomer(customerId) {
    customersManager.editCustomer(customerId);
}

function deleteCustomer(customerId) {
    customersManager.deleteCustomer(customerId);
}

function viewCustomerDetails(customerId) {
    customersManager.viewCustomerDetails(customerId);
}

function searchCustomers() {
    customersManager.searchCustomers();
}

function filterCustomers() {
    customersManager.filterCustomers();
}

function resetFilters() {
    customersManager.resetFilters();
}

function exportCustomers() {
    customersManager.exportCustomers();
}

function changePage(page) {
    customersManager.changePage(page);
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        Auth.logout();
    }
}

// Initialize Customers Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.customersManager = new CustomersManager();
});
