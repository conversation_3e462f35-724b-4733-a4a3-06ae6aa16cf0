<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-neumorphic">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <a class="navbar-brand text-gradient-primary fw-bold" href="dashboard.html">
                <i class="bi bi-cash-register me-2"></i>
                نقطة البيع
            </a>
            
            <div class="d-flex align-items-center">
                <button class="btn neumorphic-btn-secondary me-2" onclick="clearCart()">
                    <i class="bi bi-trash me-1"></i>
                    مسح السلة
                </button>
                <div class="dropdown">
                    <button class="btn neumorphic-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="currentUserName">المدير</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar-neumorphic" id="sidebar">
        <div class="text-center mb-4">
            <div class="logo-container mx-auto mb-3">
                <i class="bi bi-cash-register text-primary"></i>
            </div>
            <h5 class="text-gradient-primary">نقطة البيع</h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="sidebar-item" href="dashboard.html">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            <a class="sidebar-item active" href="pos.html">
                <i class="bi bi-cash-register"></i>
                نقطة البيع
            </a>
            <a class="sidebar-item" href="products.html">
                <i class="bi bi-box-seam"></i>
                إدارة المنتجات
            </a>
            <a class="sidebar-item" href="customers.html">
                <i class="bi bi-people"></i>
                إدارة العملاء
            </a>
            <a class="sidebar-item" href="reports.html">
                <i class="bi bi-graph-up"></i>
                التقارير
            </a>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid p-4">
            <!-- Search and Barcode Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">البحث عن منتج</label>
                                <div class="input-group">
                                    <input type="text" class="form-control neumorphic-input" id="productSearch" 
                                           placeholder="ابحث بالاسم أو الباركود...">
                                    <button class="btn neumorphic-btn-primary" onclick="searchProduct()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">مسح الباركود</label>
                                <div class="input-group">
                                    <input type="text" class="form-control neumorphic-input" id="barcodeInput" 
                                           placeholder="امسح الباركود أو اكتبه...">
                                    <button class="btn neumorphic-btn-success" onclick="addByBarcode()">
                                        <i class="bi bi-upc-scan"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- POS Grid -->
            <div class="pos-grid">
                <!-- Products Section -->
                <div class="neumorphic-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4>المنتجات</h4>
                        <div class="btn-group" role="group">
                            <button class="btn neumorphic-btn-secondary btn-sm active" onclick="filterByCategory('all')">الكل</button>
                            <button class="btn neumorphic-btn-secondary btn-sm" onclick="filterByCategory('مواد غذائية')">مواد غذائية</button>
                            <button class="btn neumorphic-btn-secondary btn-sm" onclick="filterByCategory('إلكترونيات')">إلكترونيات</button>
                            <button class="btn neumorphic-btn-secondary btn-sm" onclick="filterByCategory('ملابس')">ملابس</button>
                        </div>
                    </div>
                    
                    <div class="product-grid" id="productGrid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Cart Section -->
                <div class="neumorphic-card">
                    <h4 class="mb-3">سلة التسوق</h4>
                    
                    <!-- Customer Selection -->
                    <div class="mb-3">
                        <label class="form-label">العميل</label>
                        <select class="form-select neumorphic-input" id="customerSelect">
                            <option value="">عميل نقدي</option>
                        </select>
                    </div>
                    
                    <!-- Cart Items -->
                    <div id="cartItems" class="mb-3" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-4" id="emptyCartMessage">
                            <i class="bi bi-cart display-4"></i>
                            <p class="mt-2">السلة فارغة</p>
                        </div>
                    </div>
                    
                    <!-- Cart Summary -->
                    <div class="cart-summary">
                        <div class="row mb-2">
                            <div class="col-6">المجموع الفرعي:</div>
                            <div class="col-6 text-end" id="subtotal">0.00 ر.س</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-6">الضريبة (15%):</div>
                            <div class="col-6 text-end" id="tax">0.00 ر.س</div>
                        </div>
                        <hr>
                        <div class="row mb-3">
                            <div class="col-6"><strong>الإجمالي:</strong></div>
                            <div class="col-6 text-end"><strong id="total">0.00 ر.س</strong></div>
                        </div>
                        
                        <!-- Payment Method -->
                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select neumorphic-input" id="paymentMethod">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button class="btn neumorphic-btn-success" onclick="completeSale()" id="completeSaleBtn" disabled>
                                <i class="bi bi-check-circle me-2"></i>
                                إتمام البيع
                            </button>
                            <button class="btn neumorphic-btn-secondary" onclick="holdSale()">
                                <i class="bi bi-pause-circle me-2"></i>
                                تعليق البيع
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Modal -->
    <div class="modal fade modal-neumorphic" id="invoiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فاتورة البيع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="invoiceContent">
                    <!-- Invoice content will be generated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn neumorphic-btn-primary" onclick="printInvoice()">
                        <i class="bi bi-printer me-2"></i>طباعة
                    </button>
                    <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/pos.js"></script>
</body>
</html>
