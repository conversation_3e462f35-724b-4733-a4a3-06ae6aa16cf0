// POS (Point of Sale) Functionality for Arabic POS System

class POSManager {
    constructor() {
        this.cart = [];
        this.currentCustomer = null;
        this.taxRate = 0.15; // 15% VAT
        this.products = [];
        this.customers = [];
        this.settings = {};
        
        this.initializePOS();
    }
    
    initializePOS() {
        // Check authentication
        if (!Auth.requireAuth()) {
            return;
        }
        
        // Load data
        this.loadData();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Load products grid
        this.loadProductsGrid();
        
        // Load customers dropdown
        this.loadCustomersDropdown();
        
        // Setup mobile menu
        this.setupMobileMenu();
        
        // Set user name
        this.setUserName();
        
        // Setup barcode scanner
        this.setupBarcodeScanner();
    }
    
    loadData() {
        this.products = Storage.getProducts().filter(p => p.isActive);
        this.customers = Storage.getCustomers();
        this.settings = Storage.getSettings() || {};
        this.taxRate = (this.settings.taxRate || 15) / 100;
    }
    
    setupEventListeners() {
        // Product search
        const productSearch = document.getElementById('productSearch');
        productSearch.addEventListener('input', this.debounce(() => {
            this.searchProducts(productSearch.value);
        }, 300));
        
        // Barcode input
        const barcodeInput = document.getElementById('barcodeInput');
        barcodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addByBarcode();
            }
        });
        
        // Customer selection
        const customerSelect = document.getElementById('customerSelect');
        customerSelect.addEventListener('change', (e) => {
            this.selectCustomer(e.target.value);
        });
        
        // Payment method change
        const paymentMethod = document.getElementById('paymentMethod');
        paymentMethod.addEventListener('change', () => {
            this.updateCartSummary();
        });
    }
    
    setupMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
        });
        
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        });
    }
    
    setUserName() {
        const currentUser = Auth.getCurrentUser();
        if (currentUser) {
            document.getElementById('currentUserName').textContent = currentUser.name;
        }
    }
    
    setupBarcodeScanner() {
        // Focus on barcode input for quick scanning
        document.getElementById('barcodeInput').focus();
        
        // Auto-focus barcode input when no other input is focused
        document.addEventListener('keydown', (e) => {
            const activeElement = document.activeElement;
            if (!activeElement || activeElement.tagName !== 'INPUT') {
                if (e.key.match(/[0-9]/)) {
                    document.getElementById('barcodeInput').focus();
                }
            }
        });
    }
    
    loadProductsGrid() {
        const productGrid = document.getElementById('productGrid');
        
        if (this.products.length === 0) {
            productGrid.innerHTML = `
                <div class="col-12 text-center text-muted py-4">
                    <i class="bi bi-box-seam display-4"></i>
                    <p class="mt-2">لا توجد منتجات متاحة</p>
                    <a href="products.html" class="btn neumorphic-btn-primary">إضافة منتجات</a>
                </div>
            `;
            return;
        }
        
        productGrid.innerHTML = this.products.map(product => `
            <div class="product-card" onclick="addToCart(${product.id})" data-category="${product.category}">
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}">` : 
                        `<div class="placeholder-image"><i class="bi bi-box-seam"></i></div>`
                    }
                </div>
                <h6>${product.name}</h6>
                <p class="price">${product.price.toFixed(2)} ر.س</p>
                <small class="text-muted">المخزون: ${product.stock}</small>
                ${product.stock <= 5 ? '<span class="badge bg-gradient-danger">مخزون قليل</span>' : ''}
            </div>
        `).join('');
    }
    
    loadCustomersDropdown() {
        const customerSelect = document.getElementById('customerSelect');
        
        customerSelect.innerHTML = '<option value="">عميل نقدي</option>' +
            this.customers.map(customer => 
                `<option value="${customer.id}">${customer.name} - ${customer.phone}</option>`
            ).join('');
    }
    
    searchProducts(query) {
        if (!query.trim()) {
            this.loadProductsGrid();
            return;
        }
        
        const filteredProducts = this.products.filter(product =>
            product.name.toLowerCase().includes(query.toLowerCase()) ||
            product.barcode.includes(query) ||
            product.category.toLowerCase().includes(query.toLowerCase())
        );
        
        this.displayFilteredProducts(filteredProducts);
    }
    
    displayFilteredProducts(products) {
        const productGrid = document.getElementById('productGrid');
        
        if (products.length === 0) {
            productGrid.innerHTML = `
                <div class="col-12 text-center text-muted py-4">
                    <i class="bi bi-search display-4"></i>
                    <p class="mt-2">لم يتم العثور على منتجات</p>
                </div>
            `;
            return;
        }
        
        productGrid.innerHTML = products.map(product => `
            <div class="product-card" onclick="addToCart(${product.id})" data-category="${product.category}">
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}">` : 
                        `<div class="placeholder-image"><i class="bi bi-box-seam"></i></div>`
                    }
                </div>
                <h6>${product.name}</h6>
                <p class="price">${product.price.toFixed(2)} ر.س</p>
                <small class="text-muted">المخزون: ${product.stock}</small>
                ${product.stock <= 5 ? '<span class="badge bg-gradient-danger">مخزون قليل</span>' : ''}
            </div>
        `).join('');
    }
    
    filterByCategory(category) {
        // Update active button
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
        
        if (category === 'all') {
            this.loadProductsGrid();
            return;
        }
        
        const filteredProducts = this.products.filter(product => 
            product.category === category
        );
        
        this.displayFilteredProducts(filteredProducts);
    }
    
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) {
            this.showNotification('المنتج غير موجود', 'danger');
            return;
        }
        
        if (product.stock <= 0) {
            this.showNotification('المنتج غير متوفر في المخزون', 'warning');
            return;
        }
        
        // Check if product already in cart
        const existingItem = this.cart.find(item => item.productId === productId);
        
        if (existingItem) {
            if (existingItem.quantity >= product.stock) {
                this.showNotification('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', 'warning');
                return;
            }
            existingItem.quantity++;
        } else {
            this.cart.push({
                productId: productId,
                name: product.name,
                price: product.price,
                quantity: 1,
                total: product.price
            });
        }
        
        this.updateCartDisplay();
        this.updateCartSummary();
        
        // Show success notification
        this.showNotification(`تم إضافة ${product.name} إلى السلة`, 'success', 2000);
    }
    
    addByBarcode() {
        const barcodeInput = document.getElementById('barcodeInput');
        const barcode = barcodeInput.value.trim();
        
        if (!barcode) {
            this.showNotification('يرجى إدخال الباركود', 'warning');
            return;
        }
        
        const product = Storage.getProductByBarcode(barcode);
        if (!product) {
            this.showNotification('المنتج غير موجود', 'danger');
            barcodeInput.value = '';
            return;
        }
        
        this.addToCart(product.id);
        barcodeInput.value = '';
        barcodeInput.focus();
    }
    
    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const emptyMessage = document.getElementById('emptyCartMessage');
        
        if (this.cart.length === 0) {
            emptyMessage.style.display = 'block';
            cartItems.innerHTML = `
                <div class="text-center text-muted py-4" id="emptyCartMessage">
                    <i class="bi bi-cart display-4"></i>
                    <p class="mt-2">السلة فارغة</p>
                </div>
            `;
            document.getElementById('completeSaleBtn').disabled = true;
            return;
        }
        
        cartItems.innerHTML = this.cart.map((item, index) => `
            <div class="cart-item">
                <div class="cart-item-info">
                    <strong>${item.name}</strong>
                    <br>
                    <small class="text-muted">${item.price.toFixed(2)} ر.س × ${item.quantity}</small>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="decreaseQuantity(${index})">
                        <i class="bi bi-dash"></i>
                    </button>
                    <span class="mx-2">${item.quantity}</span>
                    <button class="quantity-btn" onclick="increaseQuantity(${index})">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button class="quantity-btn text-danger ms-2" onclick="removeFromCart(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        
        document.getElementById('completeSaleBtn').disabled = false;
    }
    
    updateCartSummary() {
        const subtotal = this.cart.reduce((sum, item) => {
            item.total = item.price * item.quantity;
            return sum + item.total;
        }, 0);
        
        const tax = subtotal * this.taxRate;
        const total = subtotal + tax;
        
        document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
        document.getElementById('tax').textContent = tax.toFixed(2) + ' ر.س';
        document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';
    }
    
    increaseQuantity(index) {
        const item = this.cart[index];
        const product = this.products.find(p => p.id === item.productId);
        
        if (item.quantity >= product.stock) {
            this.showNotification('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', 'warning');
            return;
        }
        
        item.quantity++;
        this.updateCartDisplay();
        this.updateCartSummary();
    }
    
    decreaseQuantity(index) {
        const item = this.cart[index];
        
        if (item.quantity > 1) {
            item.quantity--;
            this.updateCartDisplay();
            this.updateCartSummary();
        } else {
            this.removeFromCart(index);
        }
    }
    
    removeFromCart(index) {
        this.cart.splice(index, 1);
        this.updateCartDisplay();
        this.updateCartSummary();
    }
    
    clearCart() {
        if (this.cart.length === 0) {
            return;
        }
        
        if (confirm('هل أنت متأكد من مسح السلة؟')) {
            this.cart = [];
            this.currentCustomer = null;
            document.getElementById('customerSelect').value = '';
            this.updateCartDisplay();
            this.updateCartSummary();
            this.showNotification('تم مسح السلة', 'info');
        }
    }
    
    selectCustomer(customerId) {
        if (customerId) {
            this.currentCustomer = this.customers.find(c => c.id == customerId);
        } else {
            this.currentCustomer = null;
        }
    }
    
    completeSale() {
        if (this.cart.length === 0) {
            this.showNotification('السلة فارغة', 'warning');
            return;
        }
        
        // Calculate totals
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * this.taxRate;
        const total = subtotal + tax;
        
        // Create sale record
        const sale = {
            customerId: this.currentCustomer ? this.currentCustomer.id : null,
            customerName: this.currentCustomer ? this.currentCustomer.name : 'عميل نقدي',
            items: this.cart.map(item => ({
                productId: item.productId,
                name: item.name,
                price: item.price,
                quantity: item.quantity,
                total: item.price * item.quantity
            })),
            subtotal: subtotal,
            tax: tax,
            total: total,
            paymentMethod: document.getElementById('paymentMethod').value,
            cashier: Auth.getCurrentUser().name,
            cashierId: Auth.getCurrentUser().id
        };
        
        // Save sale
        if (Storage.addSale(sale)) {
            this.showInvoice(sale);
            this.clearCart();
            this.showNotification('تم إتمام البيع بنجاح', 'success');
            
            // Log activity
            Auth.logActivity('sale_completed', `إتمام بيع بقيمة ${total.toFixed(2)} ر.س`);
        } else {
            this.showNotification('حدث خطأ أثناء حفظ البيع', 'danger');
        }
    }
    
    showInvoice(sale) {
        const invoiceContent = document.getElementById('invoiceContent');
        const settings = this.settings;
        
        invoiceContent.innerHTML = `
            <div class="invoice-container">
                <div class="text-center mb-4">
                    <h3>${settings.storeName || 'متجر نقاط البيع'}</h3>
                    <p class="mb-1">${settings.storeAddress || ''}</p>
                    <p class="mb-1">هاتف: ${settings.storePhone || ''}</p>
                    <p class="mb-3">بريد إلكتروني: ${settings.storeEmail || ''}</p>
                    <hr>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>رقم الفاتورة:</strong> #${sale.id}
                    </div>
                    <div class="col-6 text-end">
                        <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>العميل:</strong> ${sale.customerName}
                    </div>
                    <div class="col-6 text-end">
                        <strong>الكاشير:</strong> ${sale.cashier}
                    </div>
                </div>
                
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sale.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${item.price.toFixed(2)} ر.س</td>
                                <td>${item.quantity}</td>
                                <td>${item.total.toFixed(2)} ر.س</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                <div class="row">
                    <div class="col-6"></div>
                    <div class="col-6">
                        <table class="table">
                            <tr>
                                <td>المجموع الفرعي:</td>
                                <td class="text-end">${sale.subtotal.toFixed(2)} ر.س</td>
                            </tr>
                            <tr>
                                <td>الضريبة (${(this.taxRate * 100).toFixed(0)}%):</td>
                                <td class="text-end">${sale.tax.toFixed(2)} ر.س</td>
                            </tr>
                            <tr class="table-dark">
                                <td><strong>الإجمالي:</strong></td>
                                <td class="text-end"><strong>${sale.total.toFixed(2)} ر.س</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted">${settings.receiptFooter || 'شكراً لزيارتكم'}</p>
                </div>
            </div>
        `;
        
        const invoiceModal = new bootstrap.Modal(document.getElementById('invoiceModal'));
        invoiceModal.show();
    }
    
    printInvoice() {
        const invoiceContent = document.getElementById('invoiceContent').innerHTML;
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>فاتورة البيع</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; }
                        .text-center { text-align: center; }
                        .text-end { text-align: left; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${invoiceContent}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
    
    holdSale() {
        if (this.cart.length === 0) {
            this.showNotification('السلة فارغة', 'warning');
            return;
        }
        
        // Save current cart to localStorage for later retrieval
        const heldSale = {
            cart: this.cart,
            customer: this.currentCustomer,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('held_sale', JSON.stringify(heldSale));
        this.clearCart();
        this.showNotification('تم تعليق البيع', 'info');
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    showNotification(message, type = 'info', duration = 3000) {
        // Use the global notification system from app.js
        if (window.POSApp) {
            window.POSApp.showNotification(message, type, duration);
        } else {
            alert(message);
        }
    }
}

// Global functions for HTML onclick events
function addToCart(productId) {
    posManager.addToCart(productId);
}

function addByBarcode() {
    posManager.addByBarcode();
}

function searchProduct() {
    const query = document.getElementById('productSearch').value;
    posManager.searchProducts(query);
}

function filterByCategory(category) {
    posManager.filterByCategory(category);
}

function increaseQuantity(index) {
    posManager.increaseQuantity(index);
}

function decreaseQuantity(index) {
    posManager.decreaseQuantity(index);
}

function removeFromCart(index) {
    posManager.removeFromCart(index);
}

function clearCart() {
    posManager.clearCart();
}

function completeSale() {
    posManager.completeSale();
}

function holdSale() {
    posManager.holdSale();
}

function printInvoice() {
    posManager.printInvoice();
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        Auth.logout();
    }
}

// Initialize POS when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.posManager = new POSManager();
});
