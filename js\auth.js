// Authentication and Security Management for Arabic POS System

class AuthManager {
    constructor() {
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes in milliseconds
        this.sessionKey = 'pos_session';
        this.currentUser = null;
        this.sessionTimer = null;
        
        this.initializeSession();
    }
    
    // Initialize session on page load
    initializeSession() {
        const sessionData = this.getSessionData();
        if (sessionData && this.isSessionValid(sessionData)) {
            this.currentUser = sessionData.user;
            this.startSessionTimer();
        } else {
            this.clearSession();
        }
    }
    
    // Login method
    login(username, password) {
        try {
            // Validate input
            if (!username || !password) {
                return false;
            }
            
            // Check credentials against stored users
            const user = Storage.getUserByCredentials(username, password);
            if (!user) {
                return false;
            }
            
            // Create session
            const sessionData = {
                user: {
                    id: user.id,
                    username: user.username,
                    name: user.name,
                    role: user.role,
                    email: user.email
                },
                loginTime: new Date().toISOString(),
                expiresAt: new Date(Date.now() + this.sessionTimeout).toISOString(),
                sessionId: this.generateSessionId()
            };
            
            // Save session
            this.saveSessionData(sessionData);
            this.currentUser = sessionData.user;
            Storage.setCurrentUser(this.currentUser);
            
            // Start session timer
            this.startSessionTimer();
            
            // Log login activity
            this.logActivity('login', `تسجيل دخول المستخدم: ${user.name}`);
            
            return true;
        } catch (error) {
            console.error('Login error:', error);
            return false;
        }
    }
    
    // Logout method
    logout() {
        try {
            if (this.currentUser) {
                this.logActivity('logout', `تسجيل خروج المستخدم: ${this.currentUser.name}`);
            }
            
            this.clearSession();
            this.stopSessionTimer();
            
            // Redirect to login page
            if (window.location.pathname !== '/index.html' && window.location.pathname !== '/') {
                window.location.href = 'index.html';
            }
            
            return true;
        } catch (error) {
            console.error('Logout error:', error);
            return false;
        }
    }
    
    // Check if user is logged in
    isLoggedIn() {
        const sessionData = this.getSessionData();
        return sessionData && this.isSessionValid(sessionData);
    }
    
    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }
    
    // Check if session is valid
    isSessionValid(sessionData) {
        if (!sessionData || !sessionData.expiresAt) {
            return false;
        }
        
        const expiresAt = new Date(sessionData.expiresAt);
        const now = new Date();
        
        return now < expiresAt;
    }
    
    // Extend session
    extendSession() {
        const sessionData = this.getSessionData();
        if (sessionData && this.isSessionValid(sessionData)) {
            sessionData.expiresAt = new Date(Date.now() + this.sessionTimeout).toISOString();
            this.saveSessionData(sessionData);
            this.startSessionTimer();
            return true;
        }
        return false;
    }
    
    // Generate unique session ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Session data management
    getSessionData() {
        try {
            const data = localStorage.getItem(this.sessionKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading session data:', error);
            return null;
        }
    }
    
    saveSessionData(sessionData) {
        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            return true;
        } catch (error) {
            console.error('Error saving session data:', error);
            return false;
        }
    }
    
    clearSession() {
        localStorage.removeItem(this.sessionKey);
        Storage.clearCurrentUser();
        this.currentUser = null;
    }
    
    // Session timer management
    startSessionTimer() {
        this.stopSessionTimer();
        
        this.sessionTimer = setTimeout(() => {
            this.handleSessionExpiry();
        }, this.sessionTimeout);
    }
    
    stopSessionTimer() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
            this.sessionTimer = null;
        }
    }
    
    handleSessionExpiry() {
        this.showSessionExpiredDialog();
    }
    
    showSessionExpiredDialog() {
        if (confirm('انتهت صلاحية الجلسة. هل تريد تسجيل الدخول مرة أخرى؟')) {
            this.logout();
        } else {
            this.logout();
        }
    }
    
    // Activity logging
    logActivity(action, description) {
        try {
            const activities = this.getActivities();
            const activity = {
                id: Date.now(),
                action: action,
                description: description,
                user: this.currentUser ? this.currentUser.name : 'غير معروف',
                userId: this.currentUser ? this.currentUser.id : null,
                timestamp: new Date().toISOString(),
                ip: 'localhost', // In a real app, you'd get the actual IP
                userAgent: navigator.userAgent
            };
            
            activities.unshift(activity);
            
            // Keep only last 1000 activities
            if (activities.length > 1000) {
                activities.splice(1000);
            }
            
            localStorage.setItem('pos_activities', JSON.stringify(activities));
        } catch (error) {
            console.error('Error logging activity:', error);
        }
    }
    
    getActivities() {
        try {
            const data = localStorage.getItem('pos_activities');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error reading activities:', error);
            return [];
        }
    }
    
    // Permission checking
    hasPermission(permission) {
        if (!this.currentUser) {
            return false;
        }
        
        // Admin has all permissions
        if (this.currentUser.role === 'admin') {
            return true;
        }
        
        // Define role-based permissions
        const rolePermissions = {
            'cashier': ['pos', 'customers_view', 'products_view'],
            'manager': ['pos', 'customers', 'products', 'reports_view'],
            'admin': ['*'] // All permissions
        };
        
        const userPermissions = rolePermissions[this.currentUser.role] || [];
        return userPermissions.includes('*') || userPermissions.includes(permission);
    }
    
    // Require authentication for pages
    requireAuth() {
        if (!this.isLoggedIn()) {
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }
    
    // Require specific permission
    requirePermission(permission) {
        if (!this.requireAuth()) {
            return false;
        }
        
        if (!this.hasPermission(permission)) {
            this.showAccessDenied();
            return false;
        }
        
        return true;
    }
    
    showAccessDenied() {
        alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
    }
    
    // Password validation
    validatePassword(password) {
        const minLength = 6;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        
        const errors = [];
        
        if (password.length < minLength) {
            errors.push(`كلمة المرور يجب أن تكون ${minLength} أحرف على الأقل`);
        }
        
        if (!hasNumbers) {
            errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // Change password
    changePassword(currentPassword, newPassword) {
        if (!this.currentUser) {
            return { success: false, message: 'يجب تسجيل الدخول أولاً' };
        }
        
        // Validate current password
        const user = Storage.getUserByCredentials(this.currentUser.username, currentPassword);
        if (!user) {
            return { success: false, message: 'كلمة المرور الحالية غير صحيحة' };
        }
        
        // Validate new password
        const validation = this.validatePassword(newPassword);
        if (!validation.isValid) {
            return { success: false, message: validation.errors.join('\n') };
        }
        
        // Update password
        const users = Storage.getUsers();
        const userIndex = users.findIndex(u => u.id === this.currentUser.id);
        if (userIndex !== -1) {
            users[userIndex].password = newPassword;
            users[userIndex].updatedAt = new Date().toISOString();
            Storage.saveUsers(users);
            
            this.logActivity('password_change', 'تغيير كلمة المرور');
            
            return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };
        }
        
        return { success: false, message: 'حدث خطأ أثناء تغيير كلمة المرور' };
    }
    
    // Auto-extend session on user activity
    setupActivityMonitoring() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                if (this.isLoggedIn()) {
                    this.extendSession();
                }
            }, true);
        });
    }
}

// Create global instance
const Auth = new AuthManager();

// Setup activity monitoring when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    Auth.setupActivityMonitoring();
});
