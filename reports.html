<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام نقاط البيع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-neumorphic">
        <div class="container-fluid">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="bi bi-list"></i>
            </button>
            
            <a class="navbar-brand text-gradient-primary fw-bold" href="dashboard.html">
                <i class="bi bi-graph-up me-2"></i>
                التقارير
            </a>
            
            <div class="d-flex align-items-center">
                <button class="btn neumorphic-btn-primary me-2" onclick="exportReport()">
                    <i class="bi bi-download me-1"></i>
                    تصدير التقرير
                </button>
                <div class="dropdown">
                    <button class="btn neumorphic-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="currentUserName">المدير</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar-neumorphic" id="sidebar">
        <div class="text-center mb-4">
            <div class="logo-container mx-auto mb-3">
                <i class="bi bi-graph-up text-primary"></i>
            </div>
            <h5 class="text-gradient-primary">التقارير</h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="sidebar-item" href="dashboard.html">
                <i class="bi bi-speedometer2"></i>
                لوحة التحكم
            </a>
            <a class="sidebar-item" href="pos.html">
                <i class="bi bi-cash-register"></i>
                نقطة البيع
            </a>
            <a class="sidebar-item" href="products.html">
                <i class="bi bi-box-seam"></i>
                إدارة المنتجات
            </a>
            <a class="sidebar-item" href="customers.html">
                <i class="bi bi-people"></i>
                إدارة العملاء
            </a>
            <a class="sidebar-item active" href="reports.html">
                <i class="bi bi-graph-up"></i>
                التقارير
            </a>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid p-4">
            <!-- Report Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <h5 class="mb-3">فلاتر التقرير</h5>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع التقرير</label>
                                <select class="form-select neumorphic-input" id="reportType" onchange="changeReportType()">
                                    <option value="sales">تقرير المبيعات</option>
                                    <option value="products">تقرير المنتجات</option>
                                    <option value="customers">تقرير العملاء</option>
                                    <option value="inventory">تقرير المخزون</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفترة الزمنية</label>
                                <select class="form-select neumorphic-input" id="timePeriod" onchange="changePeriod()">
                                    <option value="today">اليوم</option>
                                    <option value="yesterday">أمس</option>
                                    <option value="this_week">هذا الأسبوع</option>
                                    <option value="last_week">الأسبوع الماضي</option>
                                    <option value="this_month">هذا الشهر</option>
                                    <option value="last_month">الشهر الماضي</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="customDateRange" style="display: none;">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control neumorphic-input" id="startDate">
                            </div>
                            <div class="col-md-3" id="customDateRange2" style="display: none;">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control neumorphic-input" id="endDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn neumorphic-btn-primary" onclick="generateReport()">
                                        <i class="bi bi-search"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="row mb-4" id="summaryStats">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-primary">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <div class="stat-value" id="totalRevenue">0</div>
                        <div class="stat-label">إجمالي الإيرادات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-success">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div class="stat-value" id="totalOrders">0</div>
                        <div class="stat-label">عدد الطلبات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-info">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <div class="stat-value" id="averageOrder">0</div>
                        <div class="stat-label">متوسط الطلب</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-warning">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="stat-value" id="totalItems">0</div>
                        <div class="stat-label">عدد الأصناف المباعة</div>
                    </div>
                </div>
            </div>

            <!-- Chart Section -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="neumorphic-card">
                        <h5 class="mb-3">الرسم البياني</h5>
                        <div class="chart-container">
                            <canvas id="mainChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="neumorphic-card">
                        <h5 class="mb-3">التوزيع</h5>
                        <div class="chart-container">
                            <canvas id="pieChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Report Table -->
            <div class="row">
                <div class="col-12">
                    <div class="neumorphic-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 id="reportTitle">تقرير مفصل</h5>
                            <div class="btn-group">
                                <button class="btn neumorphic-btn-secondary btn-sm" onclick="printReport()">
                                    <i class="bi bi-printer me-1"></i>
                                    طباعة
                                </button>
                                <button class="btn neumorphic-btn-secondary btn-sm" onclick="exportReport()">
                                    <i class="bi bi-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-neumorphic" id="reportTable">
                                <thead id="reportTableHead">
                                    <!-- Table headers will be generated based on report type -->
                                </thead>
                                <tbody id="reportTableBody">
                                    <!-- Table data will be generated here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="صفحات التقرير">
                            <ul class="pagination justify-content-center" id="reportPagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Modal -->
    <div class="modal fade modal-neumorphic" id="printModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة الطباعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="printContent">
                    <!-- Print content will be generated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn neumorphic-btn-primary" onclick="doPrint()">
                        <i class="bi bi-printer me-2"></i>طباعة
                    </button>
                    <button type="button" class="btn neumorphic-btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/reports.js"></script>
</body>
</html>
