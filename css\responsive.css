/* Responsive Design for Arabic POS System */

/* Mobile First Approach */

/* Extra small devices (phones, 576px and down) */
@media (max-width: 575.98px) {
    .login-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .logo-container {
        width: 60px;
        height: 60px;
    }
    
    .logo-container i {
        font-size: 1.5rem;
    }
    
    .neumorphic-btn-primary,
    .neumorphic-btn-secondary,
    .neumorphic-btn-success,
    .neumorphic-btn-danger {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .sidebar-neumorphic {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: right 0.3s ease;
        margin: 0;
        border-radius: 0;
    }
    
    .sidebar-neumorphic.show {
        right: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .navbar-neumorphic {
        border-radius: 0;
        position: sticky;
        top: 0;
        z-index: 1030;
    }
    
    .mobile-menu-toggle {
        display: block;
        background: var(--surface-color);
        border: none;
        border-radius: 10px;
        padding: 8px 12px;
        box-shadow: var(--shadow-neumorphic-small);
        color: var(--dark-color);
    }
    
    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .table-neumorphic {
        font-size: 0.85rem;
    }
    
    .table-neumorphic th,
    .table-neumorphic td {
        padding: 0.75rem 0.5rem;
    }
    
    .pos-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .product-card {
        padding: 0.75rem;
    }
    
    .cart-summary {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--surface-color);
        box-shadow: 0 -5px 15px rgba(163, 177, 198, 0.3);
        padding: 1rem;
        border-radius: 20px 20px 0 0;
        z-index: 1020;
    }
    
    .modal-neumorphic .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-neumorphic .modal-content {
        border-radius: 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .pos-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .sidebar-neumorphic {
        position: fixed;
        top: 0;
        right: -100%;
        width: 300px;
        height: 100vh;
        z-index: 1050;
        transition: right 0.3s ease;
        margin: 0;
        border-radius: 0;
    }
    
    .sidebar-neumorphic.show {
        right: 0;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .product-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .pos-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .sidebar-neumorphic {
        position: relative;
        width: 100%;
        height: auto;
        margin: 1rem;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .product-grid {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .pos-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .sidebar-neumorphic {
        position: fixed;
        top: 0;
        right: 0;
        width: 280px;
        height: 100vh;
        z-index: 1000;
    }
    
    .main-content {
        margin-right: 280px;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .product-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .pos-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .sidebar-neumorphic {
        position: fixed;
        top: 0;
        right: 0;
        width: 300px;
        height: 100vh;
        z-index: 1000;
    }
    
    .main-content {
        margin-right: 300px;
    }
    
    .chart-container {
        height: 400px;
    }
}

/* Common responsive utilities */
.mobile-menu-toggle {
    display: none;
}

.pos-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.product-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.product-card {
    background: var(--surface-color);
    border-radius: 15px;
    padding: 1rem;
    box-shadow: var(--shadow-neumorphic-small);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-neumorphic);
}

.product-card img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.product-card h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.product-card .price {
    font-weight: 600;
    color: var(--primary-color);
}

.cart-item {
    background: var(--surface-color);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-neumorphic-small);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-item-info {
    flex: 1;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    background: var(--surface-color);
    border: none;
    border-radius: 8px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-neumorphic-small);
    color: var(--dark-color);
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    transform: scale(1.1);
}

.stat-card {
    background: var(--surface-color);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow-neumorphic);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-neumorphic-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--dark-color);
    font-weight: 500;
}

/* Print styles */
@media print {
    .sidebar-neumorphic,
    .navbar-neumorphic,
    .mobile-menu-toggle,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .neumorphic-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .invoice-container {
        max-width: 100% !important;
        margin: 0 !important;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #2d3748;
        --surface-color: #2d3748;
        --shadow-light: #3a4a5c;
        --shadow-dark: #1a202c;
        --dark-color: #e2e8f0;
    }
    
    body {
        color: var(--dark-color);
    }
    
    .neumorphic-input {
        color: var(--dark-color);
    }
    
    .neumorphic-input::placeholder {
        color: #a0aec0;
    }
}
