<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع والمحاسبة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="container-fluid h-100">
        <div class="row h-100 justify-content-center align-items-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card neumorphic-card">
                    <div class="text-center mb-4">
                        <div class="logo-container">
                            <i class="bi bi-shop display-1 text-primary"></i>
                        </div>
                        <h2 class="mt-3 text-dark">نظام نقاط البيع</h2>
                        <p class="text-muted">نظام شامل للمبيعات والمحاسبة</p>
                    </div>
                    
                    <form id="loginForm" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text neumorphic-input">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control neumorphic-input" id="username" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم المستخدم
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text neumorphic-input">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input type="password" class="form-control neumorphic-input" id="password" required>
                                <button class="btn neumorphic-btn-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <div class="invalid-feedback">
                                    يرجى إدخال كلمة المرور
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn neumorphic-btn-primary w-100 mb-3">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            تسجيل الدخول
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                للدخول كمدير: admin / admin123
                            </small>
                        </div>
                    </form>
                    
                    <div id="errorAlert" class="alert alert-danger d-none mt-3" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        © 2024 نظام نقاط البيع والمحاسبة. جميع الحقوق محفوظة.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3 text-primary">جاري تسجيل الدخول...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize login page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already logged in
            if (Auth.isLoggedIn()) {
                window.location.href = 'dashboard.html';
                return;
            }
            
            // Initialize form validation
            const form = document.getElementById('loginForm');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            // Toggle password visibility
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('bi-eye');
                this.querySelector('i').classList.toggle('bi-eye-slash');
            });
            
            // Handle form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (form.checkValidity()) {
                    handleLogin();
                }
                
                form.classList.add('was-validated');
            });
            
            // Load saved username if remember me was checked
            const savedUsername = localStorage.getItem('rememberedUsername');
            if (savedUsername) {
                document.getElementById('username').value = savedUsername;
                document.getElementById('rememberMe').checked = true;
            }
        });
        
        function handleLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            showLoading(true);
            
            // Simulate login delay for better UX
            setTimeout(() => {
                if (Auth.login(username, password)) {
                    if (rememberMe) {
                        localStorage.setItem('rememberedUsername', username);
                    } else {
                        localStorage.removeItem('rememberedUsername');
                    }
                    
                    window.location.href = 'dashboard.html';
                } else {
                    showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                    showLoading(false);
                }
            }, 1000);
        }
        
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorAlert.classList.remove('d-none');
            
            setTimeout(() => {
                errorAlert.classList.add('d-none');
            }, 5000);
        }
        
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            if (show) {
                overlay.classList.remove('d-none');
            } else {
                overlay.classList.add('d-none');
            }
        }
    </script>
</body>
</html>
